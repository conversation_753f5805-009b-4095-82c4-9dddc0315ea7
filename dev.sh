#!/bin/bash

# TurdParty Development Helper Script
# Manage core services and workers for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_DIR="compose"
CORE_COMPOSE="$COMPOSE_DIR/docker-compose.yml"
WORKERS_COMPOSE="$COMPOSE_DIR/docker-compose.workers.yml"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Start core services
start_core() {
    log_info "Starting core services (API, Database, Cache, Storage)..."
    docker compose -f "$CORE_COMPOSE" up -d
    log_success "Core services started"
}

# Start worker services
start_workers() {
    log_info "Starting worker services (File, VM, Injection workers)..."
    docker compose -f "$WORKERS_COMPOSE" up -d
    log_success "Worker services started"
}

# Start all services
start_all() {
    check_docker
    start_core
    sleep 5  # Wait for core services to be ready
    start_workers
    log_success "All services started successfully"
    show_status
}

# Stop core services
stop_core() {
    log_info "Stopping core services..."
    docker compose -f "$CORE_COMPOSE" down
    log_success "Core services stopped"
}

# Stop worker services
stop_workers() {
    log_info "Stopping worker services..."
    docker compose -f "$WORKERS_COMPOSE" down
    log_success "Worker services stopped"
}

# Stop all services
stop_all() {
    stop_workers
    stop_core
    log_success "All services stopped"
}

# Restart all services
restart_all() {
    log_info "Restarting all services..."
    stop_all
    sleep 2
    start_all
}

# Show service status
show_status() {
    log_info "Service Status:"
    echo ""
    
    # Core services
    echo -e "${BLUE}Core Services:${NC}"
    docker compose -f "$CORE_COMPOSE" ps
    echo ""
    
    # Worker services
    echo -e "${BLUE}Worker Services:${NC}"
    docker compose -f "$WORKERS_COMPOSE" ps
    echo ""
    
    # Service URLs
    echo -e "${BLUE}Service URLs:${NC}"
    echo "  API:           http://localhost:8000"
    echo "  API Docs:      http://localhost:8000/docs"
    echo "  Health Check:  http://localhost:8000/health/"
    echo "  MinIO Console: http://localhost:9001 (minioadmin/minioadmin)"
    echo "  Task Monitor:  http://localhost:5555 (admin/turdparty123)"
    echo ""
}

# Show logs
show_logs() {
    local service="$1"
    if [ -z "$service" ]; then
        log_info "Showing logs for all services..."
        docker compose -f "$CORE_COMPOSE" logs -f &
        docker compose -f "$WORKERS_COMPOSE" logs -f &
        wait
    else
        log_info "Showing logs for service: $service"
        if docker compose -f "$CORE_COMPOSE" ps | grep -q "$service"; then
            docker compose -f "$CORE_COMPOSE" logs -f "$service"
        elif docker compose -f "$WORKERS_COMPOSE" ps | grep -q "$service"; then
            docker compose -f "$WORKERS_COMPOSE" logs -f "$service"
        else
            log_error "Service '$service' not found"
            exit 1
        fi
    fi
}

# Health check
health_check() {
    log_info "Performing health checks..."
    
    # API health check
    if curl -s http://localhost:8000/health/ >/dev/null; then
        log_success "API service is healthy"
    else
        log_error "API service is not responding"
    fi
    
    # MinIO health check
    if curl -s http://localhost:9000/minio/health/live >/dev/null; then
        log_success "MinIO service is healthy"
    else
        log_error "MinIO service is not responding"
    fi
    
    # Task monitor health check
    if curl -s http://localhost:5555 >/dev/null; then
        log_success "Task monitor is healthy"
    else
        log_warning "Task monitor is not responding (workers may not be started)"
    fi
}

# Test file injection workflow
test_injection() {
    log_info "Testing file injection workflow..."
    
    # Create a test file
    echo "#!/bin/bash\necho 'Hello from TurdParty test file!'\ndate" > test_injection.sh
    chmod +x test_injection.sh
    
    log_info "Created test file: test_injection.sh"
    log_info "You can now upload this file via the API:"
    echo "  curl -X POST http://localhost:8000/api/v1/files/upload -F 'file=@test_injection.sh'"
    echo ""
    log_info "Then start a workflow with the returned file_id:"
    echo "  curl -X POST http://localhost:8000/api/v1/workflow/start -F 'file_id=<FILE_ID>'"
}

# Clean up everything
clean() {
    log_warning "This will remove all containers, volumes, and networks. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "Cleaning up all TurdParty resources..."
        stop_all
        docker volume prune -f
        docker network prune -f
        log_success "Cleanup completed"
    else
        log_info "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "TurdParty Development Helper Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start          Start all services (core + workers)"
    echo "  stop           Stop all services"
    echo "  restart        Restart all services"
    echo "  status         Show service status and URLs"
    echo "  logs [service] Show logs (all services or specific service)"
    echo "  health         Perform health checks"
    echo "  test           Create test injection file"
    echo "  clean          Clean up all resources"
    echo "  help           Show this help message"
    echo ""
    echo "Core Services:"
    echo "  start-core     Start only core services"
    echo "  stop-core      Stop only core services"
    echo ""
    echo "Worker Services:"
    echo "  start-workers  Start only worker services"
    echo "  stop-workers   Stop only worker services"
    echo ""
}

# Main command handling
case "${1:-help}" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    start-core)
        check_docker
        start_core
        ;;
    stop-core)
        stop_core
        ;;
    start-workers)
        check_docker
        start_workers
        ;;
    stop-workers)
        stop_workers
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    health)
        health_check
        ;;
    test)
        test_injection
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
