# TurdParty ELK Stack
# Phase 3: Elasticsearch, Logstash, Kibana for data pipeline

services:
  # Elasticsearch
  elasticsearch:
    container_name: turdpartycollab_elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - cluster.name=turdparty-cluster
      - node.name=turdparty-node-1
      - bootstrap.memory_lock=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ../services/monitoring/elk/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  # Logstash
  logstash:
    container_name: turdpartycollab_logstash
    image: docker.elastic.co/logstash/logstash:8.11.0
    ports:
      - "5044:5044"  # Beats input
      - "5000:5000"  # TCP input
      - "8080:8080"  # HTTP input
    environment:
      - "LS_JAVA_OPTS=-Xms256m -Xmx256m"
      - xpack.monitoring.enabled=false
    volumes:
      - logstash_data:/usr/share/logstash/data
      - ../services/monitoring/elk/logstash/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ../services/monitoring/elk/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ../services/monitoring/elk/logstash/templates:/usr/share/logstash/templates:ro
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600/_node/stats || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Kibana
  kibana:
    container_name: turdpartycollab_kibana
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
      - xpack.encryptedSavedObjects.encryptionKey=turdparty-kibana-encryption-key-32-chars
    volumes:
      - kibana_data:/usr/share/kibana/data
      - ../services/monitoring/elk/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  # VM Monitoring Agent (Fibratus simulation)
  vm-monitor:
    container_name: turdpartycollab_vm_monitor
    build:
      context: ../services/monitoring/fibratus
      dockerfile: Dockerfile
    environment:
      - LOGSTASH_HOST=logstash
      - LOGSTASH_PORT=5044
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - MONITOR_INTERVAL=10
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../data/monitoring_logs:/app/logs
    depends_on:
      - logstash
      - elasticsearch
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    command: ["python", "monitor.py"]

# Named volumes for ELK data persistence
volumes:
  elasticsearch_data:
    name: turdpartycollab_elasticsearch_data
  logstash_data:
    name: turdpartycollab_logstash_data
  kibana_data:
    name: turdpartycollab_kibana_data

# Use existing network from core services
networks:
  turdpartycollab_network:
    external: true
